#!/usr/bin/env node
import process from 'node:process';
import React from 'react';
import { render } from 'ink';
import meow from 'meow';
import App from './app.js';

const cli = meow(
	`
	Usage
	  $ tempo

	Options
		-h, --help    Show help
		-v, --version Print version and exit

	Examples
	  $ tempo

`,
	{
		importMeta: import.meta,
		flags: {
			help: { type: 'boolean', aliases: ['h'] },
			version: { type: 'boolean', description: 'Print version and exit' },
		},
	},
);

// eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing
if (cli.flags.help || cli.input.length > 0) {
	cli.showHelp();
	process.exit(0);
}

if (cli.flags.version) {
	cli.showVersion();
	process.exit(0);
}

render(<App />);
