import React from 'react';
import { Box, Text } from 'ink';

type DayData = {
	readonly entered: number;
	readonly expected: number;
};

type CalendarProps = {
	readonly month: number; // 1-12
	readonly year: number;
	readonly dayData?: Record<number, DayData>;
};

const daysInWeek = 7;
const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
const monthNames = [
	'January',
	'February',
	'March',
	'April',
	'May',
	'June',
	'July',
	'August',
	'September',
	'October',
	'November',
	'December',
];

function getDaysInMonth(year: number, month: number): number {
	return new Date(year, month, 0).getDate();
}

function getFirstDayOfMonth(year: number, month: number): number {
	return new Date(year, month - 1, 1).getDay();
}

function generateCalendarDays(
	year: number,
	month: number,
): Array<number | undefined> {
	const daysInMonth = getDaysInMonth(year, month);
	const firstDay = getFirstDayOfMonth(year, month);
	const days: Array<number | undefined> = [];

	// Add empty slots for days before the 1st of the month
	for (let i = 0; i < firstDay; i++) {
		days.push(undefined);
	}

	// Add all days of the month
	for (let day = 1; day <= daysInMonth; day++) {
		days.push(day);
	}

	return days;
}

function chunkArray<T>(array: T[], chunkSize: number): T[][] {
	const chunks: T[][] = [];
	for (let i = 0; i < array.length; i += chunkSize) {
		chunks.push(array.slice(i, i + chunkSize));
	}

	return chunks;
}

export default function Calendar({ month, year, dayData }: CalendarProps) {
	const days = generateCalendarDays(year, month);
	const weeks = chunkArray(days, daysInWeek);
	const monthName = monthNames[month - 1];

	// Ensure we have 6 weeks to maintain consistent height
	while (weeks.length < 5) {
		weeks.push(Array.from({ length: daysInWeek }, () => undefined));
	}

	return (
		<Box flexDirection="column" width="100%" padding={0} margin={0} gap={0}>
			{/* Month and Year Header */}
			<Box
				justifyContent="center"
				marginBottom={1}
				borderColor="red"
				borderStyle="single"
			>
				<Text bold color="cyan">
					{monthName} {year}
				</Text>
			</Box>

			{/* Calendar Grid Container */}
			<Box
				flexDirection="column"
				width="100%"
				borderStyle="single"
				borderColor="gray"
			>
				{/* Day Names Header */}
				<Box flexDirection="row" width="100%" padding={0} margin={0} gap={0}>
					{dayNames.map(day => (
						<Box
							key={day}
							width="14.28%"
							height={3}
							borderStyle="single"
							borderColor="gray"
							justifyContent="center"
							alignItems="center"
							padding={0}
							margin={0}
							gap={0}
						>
							<Text bold>{day}</Text>
						</Box>
					))}
				</Box>

				{/* Calendar Rows */}
				{weeks.map(week => {
					const weekKey =
						week.find(day => day !== undefined) ?? 'week-placeholder';
					return (
						<Box
							key={String(weekKey)}
							flexDirection="row"
							width="100%"
							padding={0}
							margin={0}
							gap={0}
						>
							{week.map((day, dayIndex) => {
								const isCurrentMonth = day !== undefined;
								const isToday =
									isCurrentMonth &&
									new Date().getDate() === day &&
									new Date().getMonth() + 1 === month &&
									new Date().getFullYear() === year;

								const info =
									isCurrentMonth && dayData ? dayData[day] : undefined;
								const entered = info ? info.entered : 0;
								const expected = info ? info.expected : 0;
								const diff = entered - expected;
								const diffColor: 'green' | 'red' | 'white' =
									diff > 0 ? 'green' : diff < 0 ? 'red' : 'white';

								return (
									<Box
										key={String(day ?? `blank-${dayIndex}`)}
										width="14.28%"
										height={7}
										borderStyle="single"
										borderColor={isToday ? 'yellow' : 'gray'}
										flexDirection="column"
										padding={0}
										margin={0}
										gap={0}
									>
										<Box width="100%" justifyContent="flex-end">
											<Text
												color={isCurrentMonth ? 'white' : 'gray'}
												bold={isToday}
											>
												{day ?? ''}
											</Text>
										</Box>
										{isCurrentMonth ? (
											<Box flexDirection="column" alignItems="center">
												<Text>
													{entered}h/{expected}h
												</Text>
												<Text color={diffColor}>
													{diff >= 0 ? '+' : ''}
													{diff}h
												</Text>
											</Box>
										) : null}
									</Box>
								);
							})}
						</Box>
					);
				})}
			</Box>
		</Box>
	);
}
