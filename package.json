{"name": "tempo", "version": "0.0.1", "license": "MIT", "bin": "dist/cli.js", "type": "module", "engines": {"node": ">=20", "pnpm": ">=9"}, "scripts": {"build": "tsc", "dev": "tsc --watch", "test": "prettier --check . && xo && ava", "start": "node dist/cli.js"}, "files": ["dist"], "dependencies": {"@inkjs/ui": "^2.0.0", "chalk": "^5.4.1", "ink": "^6.0.1", "ink-use-stdout-dimensions": "^1.0.5", "meow": "^13.2.0", "react": "^19.1.0"}, "devDependencies": {"@tsconfig/strictest": "^2.0.5", "@types/node": "^24.0.7", "@types/react": "^19.1.8", "@vdemedes/prettier-config": "^2.0.1", "ava": "^6.4.0", "eslint-config-xo-react": "^0.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "ink-testing-library": "^4.0.0", "prettier": "^3.6.2", "ts-node": "^10.9.2", "typescript": "^5.8.3", "xo": "^1.1.1"}, "ava": {"extensions": {"ts": "module", "tsx": "module"}, "nodeArguments": ["--loader=ts-node/esm"]}, "xo": {"prettier": true, "semicolon": true, "react": true, "rules": {"react/prop-types": "off", "object-curly-spacing": ["error", "always"]}}, "prettier": "./.prettierrc.json"}